<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CareMate Complete Database Schema</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .diagram-container {
            width: 100%;
            overflow-x: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fafafa;
        }
        .controls {
            margin-bottom: 20px;
            text-align: center;
        }
        .controls button {
            margin: 0 5px;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .legend {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .legend h3 {
            margin-top: 0;
            color: #333;
        }
        .legend-item {
            margin: 5px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CareMate Complete Database Schema</h1>
        
        <div class="controls">
            <button onclick="zoomIn()">Zoom In</button>
            <button onclick="zoomOut()">Zoom Out</button>
            <button onclick="resetZoom()">Reset Zoom</button>
            <button onclick="downloadSVG()">Download SVG</button>
        </div>
        
        <div class="diagram-container">
            <div id="mermaid-diagram"></div>
        </div>
        
        <div class="legend">
            <h3>Database Schema Overview</h3>
            <div class="legend-item"><strong>Core Modules:</strong></div>
            <div class="legend-item">• Identity & Authentication (Identity, Role, Permission, IdentityVerification)</div>
            <div class="legend-item">• Facility Management (Facility, Building, Floor, Room, Address)</div>
            <div class="legend-item">• Patient Management (Patient, PatientIdentifier, PatientAddress, PatientGuest)</div>
            <div class="legend-item">• Appointment Management (Appointment, AppointmentGuest, AppointmentGuestCheckin)</div>
            <div class="legend-item">• Visit Management (Visit, Guest, GuestVisit, VisitGuestCheckin)</div>
            <div class="legend-item">• Access Control (System, AccessLevel, FacilityAccessLevel, Card)</div>
            <div class="legend-item">• Device Management (Device, KioskGroup, DeviceSetting)</div>
            <div class="legend-item">• Event Processing (Event, EventTrace, EventAction, Function)</div>
            <div class="legend-item">• Notification System (Notification, NotificationEmail, NotificationText)</div>
            <div class="legend-item">• Document Management (Document, NdaTemplate, NdaAgreement)</div>
            <div class="legend-item">• Compliance & Logging (HipaaCompliance, ActivityLog, Watchlist)</div>
            <div class="legend-item">• Configuration (MasterData, GlobalConfiguration, CronConfig)</div>
            <div class="legend-item">• Geographic & Localization (Country, State, Language, Timezone)</div>
            
            <div class="legend-item" style="margin-top: 15px;"><strong>Key Features:</strong></div>
            <div class="legend-item">• All tables use UUID primary keys for enhanced security</div>
            <div class="legend-item">• Comprehensive audit trails with created_at, updated_at, created_by, updated_by</div>
            <div class="legend-item">• Soft deletes implemented where appropriate</div>
            <div class="legend-item">• Master data driven for flexible configuration</div>
            <div class="legend-item">• HIPAA compliance logging for patient data access</div>
            <div class="legend-item">• Event-driven architecture for processing workflows</div>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            er: {
                diagramPadding: 20,
                layoutDirection: 'TB',
                minEntityWidth: 100,
                minEntityHeight: 75,
                entityPadding: 15,
                stroke: '#333333',
                fill: '#ECECFF',
                fontSize: 12
            }
        });

        const diagramDefinition = `
erDiagram
    %% Core Identity and Authentication
    Identity {
        UUID identity_id PK
        UUID facility_id FK
        string first_name
        string last_name
        string email
        string phone
        integer status FK
        datetime created_at
        datetime updated_at
        UUID created_by
        UUID updated_by
    }

    IdentityVerification {
        UUID identity_verification_id PK
        UUID identity_id FK
        string password_hash
        UUID updated_by
        datetime created_at
        datetime updated_at
    }

    Role {
        UUID role_id PK
        string name
        text description
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Permission {
        UUID permission_id PK
        string name
        text description
        datetime created_at
        datetime updated_at
    }

    IdentityRole {
        UUID identity_role_id PK
        UUID identity_id FK
        UUID role_id FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    RolePermission {
        UUID role_permission_id PK
        UUID role_id FK
        UUID permission_id FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Facility Management
    Facility {
        UUID facility_id PK
        string name
        string description
        integer status FK
        integer facility_type FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Building {
        UUID building_id PK
        UUID facility_id FK
        string name
        string address
        integer year_constructed
        string building_code
        integer status FK
        integer type FK
        integer occupancy_type FK
        string phone
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Floor {
        UUID floor_id PK
        UUID facility_id FK
        UUID building_id FK
        string name
        integer floor_number
        integer max_occupancy
        integer occupancy_type FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Room {
        UUID room_id PK
        UUID facility_id FK
        UUID building_id FK
        UUID floor_id FK
        string name
        string room_number
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Address {
        UUID address_id PK
        UUID facility_id FK
        string address_line_1
        string address_line_2
        UUID country_id FK
        UUID state_id FK
        string postal_code
        string map_url
        string region
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Patient Management
    Patient {
        UUID patient_id PK
        UUID function_id FK
        string title
        string first_name
        string middle_name
        string last_name
        string suffix
        string preferred_name
        date birth_date
        integer gender FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    PatientIdentifier {
        UUID patient_identifier_id PK
        UUID patient_id FK
        integer identifier_type FK
        string identifier_value
        integer assigning_authority FK
        date effective_from
        date effective_to
        boolean active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    PatientAddress {
        UUID address_id PK
        UUID patient_id FK
        string address_line_1
        string address_line_2
        string city
        UUID country_id FK
        UUID state_id FK
        string postal_code
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    PatientGuest {
        UUID patient_guest_id PK
        UUID patient_id FK
        string first_name
        string last_name
        string email
        string phone
        string organization
        integer guest_type FK
        integer relationship_type FK
        integer relationship_status FK
        json image
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Appointment Management
    Appointment {
        UUID appointment_id PK
        UUID function_id FK
        UUID patient_id FK
        UUID facility_id FK
        datetime appointment_date
        integer type FK
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    AppointmentGuest {
        UUID appointment_guest_id PK
        UUID appointment_id FK
        UUID patient_guest_id FK
        UUID facility_id FK
        integer screening FK
        integer facility FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Visit Management
    Visit {
        UUID visit_id PK
        UUID facility_id FK
        string name
        text description
        integer type FK
        integer category FK
        integer repeat_visit FK
        datetime start_date
        datetime end_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Guest {
        UUID guest_id PK
        string first_name
        string last_name
        string email
        string phone
        string organization
        json image
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    GuestVisit {
        UUID guest_visit_id PK
        UUID guest_id FK
        UUID visit_id FK
        integer guest_status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Access Control
    System {
        UUID system_id PK
        string name
        integer system_pacs_id
        text description
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    AccessLevel {
        UUID access_level_id PK
        UUID system_id FK
        UUID facility_id FK
        UUID card_id FK
        string name
        string pacs_area_name
        text description
        string pacs_access_level_id
        integer status FK
        boolean online
        boolean requestable_self_service
        string access_level_type
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    Card {
        UUID card_id PK
        UUID identity_id FK
        string card_number
        integer card_format FK
        integer template FK
        integer status FK
        datetime expiry_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Device Management
    Device {
        UUID device_id PK
        UUID kiosk_group_id FK
        UUID facility_id FK
        UUID facility_building_id FK
        UUID facility_floor_id FK
        UUID facility_room_id FK
        string name
        string device_type
        string ip_address
        string mac_address
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    KioskGroup {
        UUID kiosk_group_id PK
        string name
        text description
        boolean is_active
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Event and Processing
    Event {
        UUID event_id PK
        UUID trace_id FK
        string parent_id
        string child_id
        string event_type
        json params
        integer order
        string queue
        datetime created_at
    }

    EventTrace {
        UUID trace_id PK
        string endpoint
        UUID function_id FK
        datetime created_at
    }

    Function {
        UUID function_id PK
        UUID application_type_id FK
        string queue
        string type
        string name
        string display_name
        text description
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Configuration and Master Data
    MasterData {
        UUID master_data_id PK
        string group
        integer key
        string value
        datetime created_at
        datetime updated_at
        datetime deleted_at
        UUID updated_by
    }

    %% Geographic Data
    Country {
        UUID country_id PK
        string name
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    State {
        UUID state_id PK
        UUID country_id FK
        string name
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Watchlist Management
    Watchlist {
        UUID watchlist_id PK
        string first_name
        string last_name
        string email
        string phone
        integer status FK
        integer reason FK
        date expiry_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Notification System
    Notification {
        UUID notification_id PK
        string name
        text description
        integer status
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Document Management
    Document {
        UUID document_id PK
        UUID identity_id FK
        UUID country_id FK
        UUID state_id FK
        string document_number
        integer document_type FK
        integer status FK
        date expiry_date
        json document_image
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Training Management
    Training {
        UUID training_id PK
        UUID identity_id FK
        string title
        text description
        integer course_type FK
        integer status FK
        integer recurrence FK
        date completion_date
        date expiry_date
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Vehicle Management
    Vehicle {
        UUID vehicle_id PK
        UUID identity_id FK
        string make
        string model
        string year
        string license_plate
        string color
        integer status FK
        datetime created_at
        datetime updated_at
        UUID updated_by
    }

    %% Relationships
    Identity ||--o{ IdentityVerification : has
    Identity ||--o{ IdentityRole : has
    Identity }o--|| Facility : belongs_to
    Role ||--o{ IdentityRole : has
    Role ||--o{ RolePermission : has
    Permission ||--o{ RolePermission : has
    Facility ||--o{ Building : contains
    Facility ||--o| Address : has
    Building ||--o{ Floor : contains
    Building }o--|| Facility : belongs_to
    Floor ||--o{ Room : contains
    Floor }o--|| Building : belongs_to
    Floor }o--|| Facility : belongs_to
    Room }o--|| Floor : belongs_to
    Room }o--|| Building : belongs_to
    Room }o--|| Facility : belongs_to
    Patient ||--o{ Appointment : has
    Patient ||--o| PatientIdentifier : has
    Patient ||--o| PatientAddress : has
    Patient ||--o{ PatientGuest : has
    Patient }o--o| Function : processed_by
    Appointment }o--|| Patient : for
    Appointment }o--|| Facility : at
    Appointment }o--o| Function : processed_by
    Appointment ||--o{ AppointmentGuest : has
    AppointmentGuest }o--|| Appointment : belongs_to
    AppointmentGuest }o--|| PatientGuest : for
    AppointmentGuest }o--|| Facility : at
    Visit }o--|| Facility : at
    Visit ||--o{ GuestVisit : has
    Guest ||--o{ GuestVisit : participates_in
    GuestVisit }o--|| Visit : belongs_to
    GuestVisit }o--|| Guest : for
    System ||--o{ AccessLevel : defines
    AccessLevel }o--|| System : belongs_to
    AccessLevel }o--|| Facility : for
    AccessLevel }o--o| Card : uses
    Card }o--|| Identity : belongs_to
    Device }o--|| KioskGroup : belongs_to
    Device }o--o| Facility : located_at
    Device }o--o| Building : located_in
    Device }o--o| Floor : located_on
    Device }o--o| Room : located_in
    KioskGroup ||--o{ Device : contains
    EventTrace ||--o{ Event : contains
    EventTrace }o--o| Function : triggered_by
    Event }o--|| EventTrace : belongs_to
    Function ||--o{ EventTrace : triggers
    Function ||--o{ Patient : processes
    Function ||--o{ Appointment : processes
    Document }o--|| Identity : belongs_to
    Document }o--o| Country : issued_in
    Document }o--o| State : issued_in
    Training }o--|| Identity : assigned_to
    Vehicle }o--|| Identity : owned_by
    Country ||--o{ State : contains
    State }o--|| Country : belongs_to
    Country ||--o{ Address : used_in
    Country ||--o{ PatientAddress : used_in
    Country ||--o{ Document : issued_in
    State ||--o{ Address : used_in
    State ||--o{ PatientAddress : used_in
    State ||--o{ Document : issued_in
        `;

        // Render the diagram
        mermaid.render('mermaid-svg', diagramDefinition).then(({svg}) => {
            document.getElementById('mermaid-diagram').innerHTML = svg;
        });

        // Zoom functionality
        let currentZoom = 1;
        
        function zoomIn() {
            currentZoom += 0.1;
            applyZoom();
        }
        
        function zoomOut() {
            currentZoom = Math.max(0.1, currentZoom - 0.1);
            applyZoom();
        }
        
        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }
        
        function applyZoom() {
            const diagram = document.getElementById('mermaid-diagram');
            diagram.style.transform = `scale(${currentZoom})`;
            diagram.style.transformOrigin = 'top left';
        }
        
        function downloadSVG() {
            const svg = document.querySelector('#mermaid-diagram svg');
            if (svg) {
                const svgData = new XMLSerializer().serializeToString(svg);
                const blob = new Blob([svgData], {type: 'image/svg+xml'});
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'caremate-database-schema.svg';
                a.click();
                URL.revokeObjectURL(url);
            }
        }
    </script>
</body>
</html>
